<script setup lang="ts">
import { computed } from "vue";
import type { TableDataItem } from "../types";
import { formatTimeLabel } from "../helpers/chartData";
import { getValueFormatter, UnitNames } from "~/utils/unit";

const props = defineProps<{
  data: TableDataItem[];
  title: string;
  measuresData: string[];
  measureUnits: Record<string, string>;
  tableMaxHeight?: string;
}>();
// 获取列的格式化函数
const getColumnFormatter = (key: string) => {
  if (key === "Timestamp") {
    return (val: string) => {
      return formatTimeLabel(val);
    };
  }
  if (props.measuresData.includes(key)) {
    const unitName = props.measureUnits?.[key];
    if (unitName && Object.values(UnitNames).includes(unitName as UnitNames)) {
      // 有效单位
      return (val: string | number) => {
        const num = Number(val);
        if (isNaN(num)) return val;

        const formatter = getValueFormatter(unitName as UnitNames);
        const result = formatter(num);

        const value =
          typeof result.value === "number"
            ? result.value.toFixed(2)
            : result.value;

        return result.unit ? `${value} ${result.unit}` : value;
      };
    } else {
      return (val: string | number) => {
        const num = Number(val);
        if (isNaN(num)) return val;
        if (Number.isInteger(num)) return num;
        return num.toFixed(2);
      };
    }
  }
  return undefined;
};

const columns = computed(() => {
  if (!props.data?.length) return [];
  return Object.keys(props.data[0]).map((key) => {
    const unitName = props.measureUnits?.[key];
    const shouldShowUnitInHeader = !(
      unitName &&
      Object.values(UnitNames).includes(unitName as UnitNames) &&
      props.measuresData.includes(key)
    );
    const displayLabel =
      shouldShowUnitInHeader && unitName ? `${key} (${unitName})` : key;

    return {
      label: displayLabel,
      key,
      format: getColumnFormatter(key),
    };
  });
});
</script>

<template>
  <div
    :style="
      props.tableMaxHeight
        ? { maxHeight: props.tableMaxHeight }
        : { height: '100%' }
    "
    class="overflow-y-auto"
  >
    <div
      class="font-bold pt-[10px] pb-[20px] text-stone-600 sticky top-0 z-10 bg-white"
    >
      {{ title }}
    </div>
    <div class="overflow-auto h-[calc(100%-76px)]">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead v-for="column in columns" :key="column.key">
              {{ column.label }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="row in data" :key="String(row.id)">
            <TableCell v-for="column in columns" :key="column.key">
              {{
                Array.isArray(row[column.key])
                  ? (row[column.key] as string[]).join(", ")
                  : column.format
                  ? column.format(row[column.key] as string)
                  : row[column.key]
              }}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>
