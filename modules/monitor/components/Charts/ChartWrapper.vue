<script setup lang="ts">
import { LoaderIcon } from "lucide-vue-next";
import ChartTips from "./ChartTips.vue";
import type { CommonChartProps } from "./types";
import { BORDER_COLOR_PALATTE } from "~/const";

const props = defineProps<
  Pick<
    CommonChartProps,
    "icon" | "iconProps" | "title" | "tips" | "colorIndex"
  > & {
    loading: boolean;
  }
>();

const borderColor = computed(() => {
  if (props.colorIndex !== undefined) {
    const color =
      BORDER_COLOR_PALATTE[props.colorIndex % BORDER_COLOR_PALATTE.length];
    return color.replace(/1\)$/, "0.3)");
  }
  return "rgba(229, 231, 235, 1)"; // 默认灰色边框
});
</script>

<template>
  <div
    class="flex flex-col min-h-0 h-full w-full border rounded-lg px-4 py-8 shadow transition-all duration-200 bg-white"
    :style="{
      borderColor: borderColor,
    }"
  >
    <div class="text-md flex items-center gap-2 mb-4 drop-shadow-sm">
      <component :is="icon" v-if="icon" :size="20" v-bind="iconProps || {}" />
      {{ title }}
      <LoaderIcon v-if="loading" :size="18" class="animate-spin" />
    </div>
    <slot />
    <ChartTips :tips="tips" />
  </div>
</template>
